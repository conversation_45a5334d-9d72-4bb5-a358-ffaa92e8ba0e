import { useEffect, useState, useCallback } from 'react';
import socketService from '@/lib/socket';
import { SocketNewBidData, SocketConnectionStatus, SocketBidData } from '@/lib/types';

export const useSocket = () => {
  const [connectionStatus, setConnectionStatus] = useState<SocketConnectionStatus>({
    connected: false,
  });

  useEffect(() => {
    // Connect to socket when hook is used
    const socket = socketService.connect();

    const handleConnect = () => {
      setConnectionStatus({ connected: true });
    };

    const handleDisconnect = (reason: string) => {
      setConnectionStatus({ 
        connected: false, 
        error: `Disconnected: ${reason}` 
      });
    };

    const handleConnectError = (error: any) => {
      setConnectionStatus({ 
        connected: false, 
        error: `Connection error: ${error.message || error}` 
      });
    };

    // Set up event listeners
    socket.on('connect', handleConnect);
    socket.on('disconnect', handleDisconnect);
    socket.on('connect_error', handleConnectError);

    // Set initial connection status
    setConnectionStatus({ connected: socket.connected });

    // Cleanup on unmount
    return () => {
      socket.off('connect', handleConnect);
      socket.off('disconnect', handleDisconnect);
      socket.off('connect_error', handleConnectError);
    };
  }, []);

  const subscribeToAuction = useCallback((auctionId: string) => {
    socketService.subscribeToAuction(auctionId);
  }, []);

  const unsubscribeFromAuction = useCallback((auctionId: string) => {
    socketService.unsubscribeFromAuction(auctionId);
  }, []);

  const placeBid = useCallback((bidData: SocketBidData) => {
    socketService.placeBid(bidData);
  }, []);

  const onNewBid = useCallback((callback: (data: SocketNewBidData) => void) => {
    socketService.onNewBid(callback);
    
    // Return cleanup function
    return () => {
      socketService.offNewBid(callback);
    };
  }, []);

  const onAuctionUpdate = useCallback((callback: (data: any) => void) => {
    socketService.onAuctionUpdate(callback);
    
    // Return cleanup function
    return () => {
      socketService.offAuctionUpdate(callback);
    };
  }, []);

  return {
    connectionStatus,
    subscribeToAuction,
    unsubscribeFromAuction,
    placeBid,
    onNewBid,
    onAuctionUpdate,
    isConnected: connectionStatus.connected,
  };
};

export default useSocket;
