import { useRef, useEffect, useState } from "react";
import { LiveReel } from "@/lib/types";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Clock, Eye, Users, TrendingUp, Gavel } from "lucide-react";
import { cn } from "@/lib/utils";

interface LiveReelCardProps {
  reel: LiveReel;
  onClick: () => void;
  className?: string;
  isInFocus?: boolean;
}

export function LiveReelCard({
  reel,
  onClick,
  className,
  isInFocus = false,
}: LiveReelCardProps) {
  const videoRef = useRef<HTMLVideoElement>(null);
  const [isPlaying, setIsPlaying] = useState(false);
  const [showVideo, setShowVideo] = useState(true); // Always show video now

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const formatTimeRemaining = (endTime: Date) => {
    const now = new Date();
    const diff = endTime.getTime() - now.getTime();
    const hours = Math.floor(diff / (1000 * 60 * 60));
    const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));

    if (hours > 0) {
      return `${hours}h ${minutes}m`;
    }
    return `${minutes}m`;
  };

  // Auto-play when in focus
  useEffect(() => {
    if (videoRef.current) {
      if (isInFocus) {
        videoRef.current
          .play()
          .then(() => {
            setIsPlaying(true);
          })
          .catch(() => {
            // Auto-play failed, continue silently
            console.log("Autoplay failed");
          });
      } else {
        videoRef.current.pause();
        setIsPlaying(false);
      }
    }
  }, [isInFocus]);

  const handleBidClick = (e: React.MouseEvent) => {
    e.stopPropagation();
    onClick(); // Navigate to details page for bidding
  };

  const handleVideoEnded = () => {
    setIsPlaying(false);
    if (videoRef.current) {
      videoRef.current.currentTime = 0;
      // Auto-restart if still in focus
      if (isInFocus) {
        videoRef.current
          .play()
          .then(() => {
            setIsPlaying(true);
          })
          .catch(() => {
            console.log("Video restart failed");
          });
      }
    }
  };

  // Generate a sample video URL
  const videoUrl = `https://commondatastorage.googleapis.com/gtv-videos-bucket/sample/BigBuckBunny.mp4#t=0.1`;

  return (
    <Card
      className={cn(
        "group cursor-pointer overflow-hidden transition-all duration-300 hover:shadow-xl hover:scale-[1.01] border-0 bg-gradient-to-br from-slate-50 to-slate-100",
        isInFocus && "ring-2 ring-blue-500 scale-[1.01] shadow-xl",
        className,
      )}
      onClick={onClick}
    >
      <div className="relative">
        {/* Horizontal layout - 16:9 aspect ratio for bigger, cinematic feel */}
        <div className="aspect-video overflow-hidden">
          {/* Video - always showing with autoplay */}
          <video
            ref={videoRef}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
            poster={reel.thumbnailUrl}
            muted
            loop
            playsInline
            onEnded={handleVideoEnded}
            onPlay={() => setIsPlaying(true)}
            onPause={() => setIsPlaying(false)}
          >
            <source src={videoUrl} type="video/mp4" />
            {/* Fallback image if video fails to load */}
            <img
              src={reel.thumbnailUrl}
              alt={reel.title}
              className="w-full h-full object-cover"
            />
          </video>

          <div className="absolute inset-0 bg-gradient-to-t from-black/70 via-black/20 to-transparent" />
          <div className="absolute inset-0 bg-gradient-to-r from-black/30 via-transparent to-black/50" />
        </div>

        {/* Live indicator */}
        {reel.isLive && (
          <div className="absolute top-4 left-4 flex items-center gap-2 bg-red-500 text-white px-3 py-1.5 rounded-full text-sm font-medium shadow-lg">
            <div className="w-2 h-2 bg-white rounded-full animate-pulse" />
            LIVE AUCTION
          </div>
        )}

        {/* Viewer count */}
        <div className="absolute top-4 right-4 flex items-center gap-2 bg-black/60 backdrop-blur-sm text-white px-3 py-1.5 rounded-full text-sm">
          <Eye className="w-4 h-4" />
          {reel.viewerCount.toLocaleString()}
        </div>

        {/* Video duration indicator */}
        <div className="absolute bottom-4 left-4 bg-black/70 backdrop-blur-sm text-white px-2 py-1 rounded text-xs font-medium">
          0:
          {Math.floor(Math.random() * 60)
            .toString()
            .padStart(2, "0")}
        </div>

        {/* Bid button - positioned at bottom right */}
        <div className="absolute bottom-4 right-4">
          <Button
            onClick={handleBidClick}
            className="bg-green-600 hover:bg-green-700 text-white font-semibold px-6 py-2 rounded-full shadow-lg transition-all duration-300 hover:scale-105 flex items-center gap-2"
          >
            <Gavel className="w-4 h-4" />
            Bid Now
          </Button>
        </div>

        {/* Content overlay - positioned on the left side */}
        <div className="absolute bottom-0 left-0 right-0 p-6">
          <div className="max-w-2xl">
            {/* Auctioneer info */}
            <div className="flex items-center gap-3 mb-3">
              <Avatar className="w-8 h-8 ring-2 ring-white/20">
                <AvatarImage src={reel.auctioneer.avatar} />
                <AvatarFallback>{reel.auctioneer.name[0]}</AvatarFallback>
              </Avatar>
              <div>
                <span className="text-white font-medium text-sm">
                  {reel.auctioneer.name}
                </span>
                <div className="flex items-center gap-1">
                  <div className="w-1 h-1 bg-yellow-400 rounded-full" />
                  <span className="text-yellow-400 text-xs">
                    {reel.auctioneer.rating}
                  </span>
                </div>
              </div>
            </div>

            <h3 className="font-bold text-xl text-white mb-3 line-clamp-2 drop-shadow-lg">
              {reel.title}
            </h3>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-xs text-gray-300 mb-1">Current Bid</div>
                <div className="text-2xl font-bold text-green-400 drop-shadow-lg">
                  {formatCurrency(reel.product.currentBid)}
                </div>
                <div className="text-xs text-gray-400">
                  Starting at {formatCurrency(reel.product.startingPrice)}
                </div>
              </div>

              <div className="text-right mr-24">
                {" "}
                {/* Add margin to avoid overlap with bid button */}
                <div className="flex flex-col gap-2">
                  <div className="flex items-center gap-1 text-white">
                    <Clock className="w-4 h-4" />
                    <span className="text-sm font-medium">
                      {formatTimeRemaining(reel.endTime)}
                    </span>
                  </div>
                  <div className="flex items-center gap-1 text-green-400">
                    <TrendingUp className="w-4 h-4" />
                    <span className="text-sm font-medium">
                      {reel.bidCount} bids
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </Card>
  );
}
