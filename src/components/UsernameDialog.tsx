import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogDescription,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON><PERSON>eader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useUsername } from "@/lib/contexts/UsernameContext";
import { toast } from "@/hooks/use-toast";

interface UsernameDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  required?: boolean;
}

export const UsernameDialog: React.FC<UsernameDialogProps> = ({
  open,
  onOpenChange,
  required = false,
}) => {
  const { username, setUsername } = useUsername();
  const [inputValue, setInputValue] = useState(username);

  const handleSave = () => {
    const trimmedValue = inputValue.trim();
    if (!trimmedValue) {
      toast({
        title: "Username Required",
        description: "Please enter a username to continue.",
        variant: "destructive",
      });
      return;
    }

    if (trimmedValue.length < 2) {
      toast({
        title: "Username Too Short",
        description: "Username must be at least 2 characters long.",
        variant: "destructive",
      });
      return;
    }

    if (trimmedValue.length > 20) {
      toast({
        title: "Username Too Long",
        description: "Username must be 20 characters or less.",
        variant: "destructive",
      });
      return;
    }

    setUsername(trimmedValue);
    onOpenChange(false);
    toast({
      title: "Username Set",
      description: `Your username has been set to "${trimmedValue}".`,
    });
  };

  const handleCancel = () => {
    if (required && !username) {
      toast({
        title: "Username Required",
        description: "You need to set a username to place bids.",
        variant: "destructive",
      });
      return;
    }
    setInputValue(username);
    onOpenChange(false);
  };

  return (
    <Dialog open={open} onOpenChange={required ? undefined : onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>
            {username ? "Change Username" : "Set Your Username"}
          </DialogTitle>
          <DialogDescription>
            {required
              ? "You need to set a username before you can place bids."
              : "Choose a username that will be displayed when you place bids."}
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid grid-cols-4 items-center gap-4">
            <Label htmlFor="username" className="text-right">
              Username
            </Label>
            <Input
              id="username"
              value={inputValue}
              onChange={(e) => setInputValue(e.target.value)}
              placeholder="Enter your username"
              className="col-span-3"
              maxLength={20}
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  handleSave();
                }
              }}
            />
          </div>
        </div>
        <DialogFooter>
          {!required && (
            <Button variant="outline" onClick={handleCancel}>
              Cancel
            </Button>
          )}
          <Button onClick={handleSave}>
            {username ? "Update" : "Set Username"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};
