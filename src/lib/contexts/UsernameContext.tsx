import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';

interface UsernameContextType {
  username: string;
  setUsername: (username: string) => void;
  hasUsername: boolean;
}

const UsernameContext = createContext<UsernameContextType | undefined>(undefined);

interface UsernameProviderProps {
  children: ReactNode;
}

export const UsernameProvider: React.FC<UsernameProviderProps> = ({ children }) => {
  const [username, setUsernameState] = useState<string>('');

  // Load username from localStorage on mount
  useEffect(() => {
    const savedUsername = localStorage.getItem('auction-username');
    if (savedUsername) {
      setUsernameState(savedUsername);
    }
  }, []);

  const setUsername = (newUsername: string) => {
    setUsernameState(newUsername);
    if (newUsername.trim()) {
      localStorage.setItem('auction-username', newUsername.trim());
    } else {
      localStorage.removeItem('auction-username');
    }
  };

  const hasUsername = username.trim().length > 0;

  return (
    <UsernameContext.Provider value={{ username, setUsername, hasUsername }}>
      {children}
    </UsernameContext.Provider>
  );
};

export const useUsername = () => {
  const context = useContext(UsernameContext);
  if (context === undefined) {
    throw new Error('useUsername must be used within a UsernameProvider');
  }
  return context;
};
