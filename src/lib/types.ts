export interface Product {
  id: string;
  name: string;
  description: string;
  category: string;
  startingPrice: number;
  currentBid: number;
  imageUrl: string;
  condition: string;
  brand?: string;
}

export interface LiveReel {
  id: string;
  title: string;
  thumbnailUrl: string;
  videoUrl: string;
  product: Product;
  auctioneer: {
    name: string;
    avatar: string;
    rating: number;
  };
  viewerCount: number;
  endTime: Date;
  isLive: boolean;
  bidCount: number;
}

export interface Bid {
  id: string;
  reelId: string;
  amount: number;
  bidder: {
    name: string;
    avatar: string;
  };
  timestamp: Date;
}

export interface BidHistory {
  reelId: string;
  bids: Bid[];
}

// Socket.IO related types
export interface SocketBidData {
  auctionId: string;
  username: string;
  price: number;
}

export interface SocketNewBidData {
  bid: {
    id: string;
    auctionId: string;
    amount: number;
    bidder: {
      name: string;
      avatar: string;
    };
    timestamp: Date;
  };
}

export interface SocketConnectionStatus {
  connected: boolean;
  error?: string;
}
