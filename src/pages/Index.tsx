import { useState, useEffect } from "react";
import { useNavigate } from "react-router-dom";
import { LiveReelCard } from "@/components/LiveReelCard";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Card, CardContent } from "@/components/ui/card";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import { liveReels, categories } from "@/lib/data";
import { LiveReel } from "@/lib/types";
import { useUsername } from "@/lib/contexts/UsernameContext";
import { UsernameDialog } from "@/components/UsernameDialog";
import {
  Search,
  Filter,
  TrendingUp,
  Clock,
  Users,
  Sparkles,
} from "lucide-react";
import { cn } from "@/lib/utils";

const Index = () => {
  const navigate = useNavigate();
  const { username, hasUsername } = useUsername();
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [filteredReels, setFilteredReels] = useState<LiveReel[]>(liveReels);
  const [api, setApi] = useState<CarouselApi>();
  const [current, setCurrent] = useState(0);
  const [showUsernameDialog, setShowUsernameDialog] = useState(false);

  // Filter reels based on search and category
  useEffect(() => {
    let filtered = liveReels;

    if (searchQuery) {
      filtered = filtered.filter(
        (reel) =>
          reel.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
          reel.product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
          reel.product.category
            .toLowerCase()
            .includes(searchQuery.toLowerCase()),
      );
    }

    if (selectedCategory) {
      filtered = filtered.filter(
        (reel) =>
          reel.product.category.toLowerCase() ===
          selectedCategory.toLowerCase(),
      );
    }

    setFilteredReels(filtered);
  }, [searchQuery, selectedCategory]);

  // Carousel tracking for auto-play
  useEffect(() => {
    if (!api) {
      return;
    }

    setCurrent(api.selectedScrollSnap());

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap());
    });
  }, [api]);

  const handleReelClick = (reelId: string) => {
    navigate(`/reel/${reelId}`);
  };

  const totalViewers = liveReels.reduce(
    (sum, reel) => sum + reel.viewerCount,
    0,
  );
  const liveAuctions = liveReels.filter((reel) => reel.isLive).length;

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100">
      {/* Header with Username */}
      <div className="bg-white/80 backdrop-blur-sm border-b">
        <div className="max-w-7xl mx-auto px-4 py-3 sm:px-6 lg:px-8">
          <div className="flex justify-end">
            {hasUsername ? (
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setShowUsernameDialog(true)}
                className="flex items-center gap-2 text-blue-600"
              >
                <Users className="w-4 h-4" />
                {username}
              </Button>
            ) : (
              <Button
                variant="outline"
                size="sm"
                onClick={() => setShowUsernameDialog(true)}
                className="flex items-center gap-2"
              >
                <Users className="w-4 h-4" />
                Set Username
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Hero Section */}
      <div className="relative overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-blue-600/20" />
        <div className="relative max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8">
          <div className="text-center">
            {/* Main Title */}
            <div className="mb-6">
              <h1
                className="text-2xl sm:text-3xl lg:text-4xl font-semibold mb-6"
                style={{ color: "rgba(20, 102, 218, 1)" }}
              >
                Live IFE Auction Experience
              </h1>
            </div>

            {/* Stats */}
            <div className="flex flex-wrap justify-center gap-6 mb-8">
              <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2">
                <Users className="w-5 h-5 text-blue-600" />
                <span className="font-medium">
                  {totalViewers.toLocaleString()} watching
                </span>
              </div>
              <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2">
                <TrendingUp className="w-5 h-5 text-green-600" />
                <span className="font-medium">
                  {liveAuctions} live auctions
                </span>
              </div>
              <div className="flex items-center gap-2 bg-white/80 backdrop-blur-sm rounded-full px-4 py-2">
                <Sparkles className="w-5 h-5 text-purple-600" />
                <span className="font-medium">Real-time bidding</span>
              </div>
            </div>

            {/* Search Bar */}
            <div className="max-w-md mx-auto">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                <Input
                  placeholder="Search auctions..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 pr-4 py-3 text-lg bg-white/90 backdrop-blur-sm border-0 shadow-lg"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Categories */}
      <div className="max-w-7xl mx-auto px-4 py-8 sm:px-6 lg:px-8">
        <div className="flex items-center gap-2 mb-6">
          <Filter className="w-5 h-5 text-gray-600" />
          <h3 className="text-lg font-semibold text-gray-800">Categories</h3>
        </div>

        <div className="flex flex-wrap gap-3 mb-8">
          <Button
            variant={selectedCategory === null ? "default" : "outline"}
            onClick={() => setSelectedCategory(null)}
            className="rounded-full"
          >
            All
          </Button>
          {categories.map((category) => (
            <Button
              key={category.id}
              variant={
                selectedCategory === category.name ? "default" : "outline"
              }
              onClick={() =>
                setSelectedCategory(
                  selectedCategory === category.name ? null : category.name,
                )
              }
              className="rounded-full flex items-center gap-2"
            >
              <span>{category.icon}</span>
              {category.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Featured Live Reels */}
      <div className="max-w-7xl mx-auto px-4 pb-16 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between mb-8">
          <div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">
              Live Auctions
            </h2>
            <p className="text-gray-600">
              Join thousands of bidders in real-time auctions
            </p>
          </div>
          <Badge
            variant="secondary"
            className="bg-red-100 text-red-700 flex items-center gap-1"
          >
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
            {filteredReels.filter((r) => r.isLive).length} Live Now
          </Badge>
        </div>

        {filteredReels.length > 0 ? (
          <div className="hidden lg:block">
            <Carousel
              opts={{
                align: "center",
                loop: true,
              }}
              setApi={setApi}
              className="w-full"
            >
              <CarouselContent className="-ml-6">
                {filteredReels.map((reel, index) => (
                  <CarouselItem key={reel.id} className="pl-6 basis-2/3">
                    <LiveReelCard
                      reel={reel}
                      onClick={() => handleReelClick(reel.id)}
                      isInFocus={index === current}
                    />
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="hidden lg:flex" />
              <CarouselNext className="hidden lg:flex" />
            </Carousel>
          </div>
        ) : (
          <Card className="p-12 text-center">
            <CardContent>
              <div className="text-gray-500 mb-4">
                <Search className="w-12 h-12 mx-auto mb-4 opacity-50" />
                <h3 className="text-xl font-semibold mb-2">
                  No auctions found
                </h3>
                <p>Try adjusting your search or category filter</p>
              </div>
              <Button
                variant="outline"
                onClick={() => {
                  setSearchQuery("");
                  setSelectedCategory(null);
                }}
              >
                Clear filters
              </Button>
            </CardContent>
          </Card>
        )}

        {/* Mobile Grid */}
        <div className="lg:hidden grid grid-cols-1 gap-6 mt-8">
          {filteredReels.map((reel, index) => (
            <LiveReelCard
              key={reel.id}
              reel={reel}
              onClick={() => handleReelClick(reel.id)}
              isInFocus={index === 0}
            />
          ))}
        </div>
      </div>

      {/* CTA Section */}
      <div className="bg-gradient-to-r from-purple-600 to-blue-600 text-white">
        <div className="max-w-7xl mx-auto px-4 py-16 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold mb-4">Ready to Start Bidding?</h2>
          <p className="text-xl text-purple-100 mb-8 max-w-2xl mx-auto">
            Join millions of users discovering unique items and winning great
            deals through live auctions.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              size="lg"
              variant="secondary"
              className="bg-white text-purple-600 hover:bg-gray-100"
            >
              Create Account
            </Button>
            <Button
              size="lg"
              variant="outline"
              className="border-white text-white hover:bg-white/10"
            >
              Learn How It Works
            </Button>
          </div>
        </div>
      </div>

      {/* Username Dialog */}
      <UsernameDialog
        open={showUsernameDialog}
        onOpenChange={setShowUsernameDialog}
      />
    </div>
  );
};

export default Index;
